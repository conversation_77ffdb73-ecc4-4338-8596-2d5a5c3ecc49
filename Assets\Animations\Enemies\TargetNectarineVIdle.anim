%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TargetNectarineVIdle
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - serializedVersion: 2
    curve:
    - time: 0
      value: {fileID: 21300000, guid: 1c58dea3b235e494284f5bc1f6a0102a, type: 3}
    - time: 0.2
      value: {fileID: 21300000, guid: d648144019b4643f9bd22dac05cc73d8, type: 3}
    - time: 0.4
      value: {fileID: 21300000, guid: 66598e853d4dc432d9ceb610221bab98, type: 3}
    - time: 0.6
      value: {fileID: 21300000, guid: a399de7e4230f435f8195b7b3bb7e1d9, type: 3}
    - time: 0.8
      value: {fileID: 21300000, guid: 40c0bdfba7a68413bbd0c1b2bba11969, type: 3}
    attribute: m_Sprite
    path: 
    classID: 212
    script: {fileID: 0}
    flags: 2
  m_SampleRate: 5
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping:
    - {fileID: 21300000, guid: 1c58dea3b235e494284f5bc1f6a0102a, type: 3}
    - {fileID: 21300000, guid: d648144019b4643f9bd22dac05cc73d8, type: 3}
    - {fileID: 21300000, guid: 66598e853d4dc432d9ceb610221bab98, type: 3}
    - {fileID: 21300000, guid: a399de7e4230f435f8195b7b3bb7e1d9, type: 3}
    - {fileID: 21300000, guid: 40c0bdfba7a68413bbd0c1b2bba11969, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 1
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
