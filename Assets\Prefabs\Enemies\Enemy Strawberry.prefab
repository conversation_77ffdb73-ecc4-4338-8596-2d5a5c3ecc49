%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &7105157813290141413
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1188166005559203625}
  - component: {fileID: 3576294929709699182}
  - component: {fileID: 370228197509443106}
  - component: {fileID: 8570108467300620612}
  m_Layer: 7
  m_Name: Avatar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1188166005559203625
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7105157813290141413}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.38, y: 0.883, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6014509365480352120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &3576294929709699182
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7105157813290141413}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 472713477
  m_SortingLayer: 3
  m_SortingOrder: 0
  m_Sprite: {fileID: 113630918889461111, guid: 6a40619dae97d4f85b6735d226532cf2, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.25, y: 1.5}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &370228197509443106
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7105157813290141413}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: dbb5b027549f543ee97b2f4bb148da83, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &8570108467300620612
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7105157813290141413}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 613f7621d83124da3ab8cfcb1297cceb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hitColor: {r: 0.7411765, g: 0.19215687, b: 0.2784314, a: 1}
--- !u!1 &8379012214942806497
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7590507126697255534}
  m_Layer: 7
  m_Name: Spawn Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7590507126697255534
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8379012214942806497}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2, y: 5, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6014509365480352120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8720719849892537440
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6014509365480352120}
  - component: {fileID: 1737654300631129846}
  m_Layer: 7
  m_Name: Enemy Strawberry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6014509365480352120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8720719849892537440}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: -4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1188166005559203625}
  - {fileID: 7590507126697255534}
  - {fileID: 6748518056509897706}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1737654300631129846
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8720719849892537440}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 714100fa971264614ba70c1d4691dd8b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  maxHealth: 8
  spawnPoint: {fileID: 8379012214942806497}
  targetPrefabs:
  - {fileID: 4466728811933992367, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
  numberOfTargets: 1
  offsetAngle: 30
  bufferAngle: 90
  activeTargets: []
  minSeeds: 1
  maxSeeds: 5
  displayName: Strawberry
--- !u!1001 &5185282290183823004
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6014509365480352120}
    m_Modifications:
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalPosition.y
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.8660254
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 60
      objectReference: {fileID: 0}
    - target: {fileID: 4466728811933992367, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
      propertyPath: m_Name
      value: Target Strawberry
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
--- !u!4 &6748518056509897706 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1896683602193884534, guid: cd4b1f2956b5141288ef86da6df73713, type: 3}
  m_PrefabInstance: {fileID: 5185282290183823004}
  m_PrefabAsset: {fileID: 0}
